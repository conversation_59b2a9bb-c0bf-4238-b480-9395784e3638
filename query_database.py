import pymysql

def query_database():
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            database='tm',
            user='root',
            password='',
            charset='utf8mb4'
        )
        
        with connection:
            cursor = connection.cursor()
            
            # Query all data from fbt_kingdee table
            query = "SELECT * FROM fbt_kingdee"
            cursor.execute(query)
            
            # Get column names
            column_names = [desc[0] for desc in cursor.description]
            print("Columns:", ", ".join(column_names))
            print("-" * 50)
            
            # Fetch and display all records
            records = cursor.fetchall()
            
            if records:
                for record in records:
                    for i, value in enumerate(record):
                        print(f"{column_names[i]}: {value}")
                    print("-" * 50)
                print(f"Total records: {len(records)}")
            else:
                print("No records found in the table.")
                
    except Exception as e:
        print(f"Error connecting to MySQL: {e}")

if __name__ == "__main__":
    query_database()